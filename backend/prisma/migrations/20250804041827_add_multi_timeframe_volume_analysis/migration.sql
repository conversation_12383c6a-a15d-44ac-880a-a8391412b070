/*
  Warnings:

  - You are about to drop the `alert_history` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `alert_rules` table. If the table is not empty, all the data it contains will be lost.

*/
-- AlterTable
ALTER TABLE "early_warning_alerts" ADD COLUMN     "multiTimeframeVolumeAnalysis" JSONB;

-- DropTable
DROP TABLE "alert_history";

-- DropTable
DROP TABLE "alert_rules";

-- CreateTable
CREATE TABLE "early_warning_alert_rules" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "minConfidence" DOUBLE PRECISION NOT NULL,
    "alertTypes" JSONB NOT NULL,
    "requiredPhases" JSONB,
    "minPhaseScore" DOUBLE PRECISION,
    "minTimeEstimate" INTEGER,
    "maxTimeEstimate" INTEGER,
    "requiredTriggers" JSONB,
    "priority" TEXT NOT NULL DEFAULT 'MEDIUM',
    "enableToast" BOOLEAN NOT NULL DEFAULT true,
    "enableSound" BOOLEAN NOT NULL DEFAULT true,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "lastTriggered" TIMESTAMP(3),
    "triggerCount" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "early_warning_alert_rules_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "early_warning_alert_history" (
    "id" TEXT NOT NULL,
    "ruleId" TEXT NOT NULL,
    "ruleName" TEXT NOT NULL,
    "earlyWarningId" TEXT NOT NULL,
    "symbol" TEXT NOT NULL,
    "alertType" TEXT NOT NULL,
    "confidence" DOUBLE PRECISION NOT NULL,
    "timeEstimateMin" INTEGER NOT NULL,
    "timeEstimateMax" INTEGER NOT NULL,
    "phase1Score" DOUBLE PRECISION NOT NULL,
    "phase2Score" DOUBLE PRECISION NOT NULL,
    "phase3Score" DOUBLE PRECISION NOT NULL,
    "triggeredBy" JSONB NOT NULL,
    "currentPrice" DOUBLE PRECISION NOT NULL,
    "volume24h" DOUBLE PRECISION,
    "priceChange24h" DOUBLE PRECISION,
    "volumeSpike" JSONB,
    "rsiMomentum" JSONB,
    "emaConvergence" JSONB,
    "bidAskImbalance" JSONB,
    "priceAction" JSONB,
    "whaleActivity" JSONB,
    "message" TEXT NOT NULL,
    "priority" TEXT NOT NULL,
    "toastShown" BOOLEAN NOT NULL DEFAULT false,
    "isRead" BOOLEAN NOT NULL DEFAULT false,
    "readAt" TIMESTAMP(3),
    "triggeredAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "early_warning_alert_history_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "early_warning_alert_rules_isActive_idx" ON "early_warning_alert_rules"("isActive");

-- CreateIndex
CREATE INDEX "early_warning_alert_rules_minConfidence_isActive_idx" ON "early_warning_alert_rules"("minConfidence", "isActive");

-- CreateIndex
CREATE INDEX "early_warning_alert_rules_priority_isActive_idx" ON "early_warning_alert_rules"("priority", "isActive");

-- CreateIndex
CREATE INDEX "early_warning_alert_history_symbol_triggeredAt_idx" ON "early_warning_alert_history"("symbol", "triggeredAt");

-- CreateIndex
CREATE INDEX "early_warning_alert_history_ruleId_triggeredAt_idx" ON "early_warning_alert_history"("ruleId", "triggeredAt");

-- CreateIndex
CREATE INDEX "early_warning_alert_history_alertType_confidence_triggeredA_idx" ON "early_warning_alert_history"("alertType", "confidence", "triggeredAt");

-- CreateIndex
CREATE INDEX "early_warning_alert_history_isRead_triggeredAt_idx" ON "early_warning_alert_history"("isRead", "triggeredAt");

-- AddForeignKey
ALTER TABLE "early_warning_alert_history" ADD CONSTRAINT "early_warning_alert_history_ruleId_fkey" FOREIGN KEY ("ruleId") REFERENCES "early_warning_alert_rules"("id") ON DELETE CASCADE ON UPDATE CASCADE;
