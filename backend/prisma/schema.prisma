// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Admin Settings for configurable thresholds and rules
model AdminSettings {
  id                    String   @id @default(cuid())
  settingKey           String   @unique
  settingValue         String
  settingType          String   // 'number', 'string', 'boolean', 'json'
  description          String?
  category             String   // 'confidence', 'strength', 'timeframe', 'notification'
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt

  @@map("admin_settings")
}

// Notification Rules for determining when to trigger alerts
model NotificationRule {
  id                   String   @id @default(cuid())
  name                 String
  description          String?
  isActive             Boolean  @default(true)

  // Rule conditions
  minConfidence        Float?   // Minimum confidence percentage (0-100)
  minStrength          Float?   // Minimum signal strength (0-100)
  requiredTimeframes   Int?     // Number of timeframes that must agree (deprecated, use specificTimeframes)
  specificTimeframes   Json?    // Array of specific timeframes to check (e.g., ["1m", "5m", "1h"])
  requiredSignalType   String?  // 'BUY', 'SELL', 'HOLD', or null for any

  // Advanced rule logic (JSON format for complex conditions)
  advancedConditions   Json?    // For complex rules like "3 out of 5 timeframes show BUY with 70%+ confidence"

  // Notification settings
  enableSound          Boolean  @default(true)
  enableVisual         Boolean  @default(true)
  priority             String   @default("MEDIUM") // 'LOW', 'MEDIUM', 'HIGH'

  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt

  // Relations
  triggeredNotifications Notification[]

  @@map("notification_rules")
}

// Signal History for storing all generated signals
model SignalHistory {
  id                   String   @id @default(cuid())
  symbol               String
  exchange             String   @default("binance")
  timeframe            String

  // Signal data
  signal               String   // 'BUY', 'SELL', 'HOLD'
  confidence           Float    // 0-100
  strength             Float    // 0-100
  currentPrice         Float

  // Technical analysis data
  technicalIndicators  Json?    // RSI, MACD, etc.
  chartPatterns        Json?    // Detected chart patterns
  candlestickPatterns  Json?    // Detected candlestick patterns
  reasoning            Json?    // Array of reasoning strings

  // Metadata
  generatedAt          DateTime @default(now())
  processingTimeMs     Int?     // Time taken to generate signal

  // Relations
  triggeredNotifications Notification[]

  @@map("signal_history")
  @@index([symbol, timeframe, generatedAt])
  @@index([signal, confidence, generatedAt])
}

// Notifications triggered by the system
model Notification {
  id                   String   @id @default(cuid())

  // Notification content
  title                String
  message              String
  type                 String   // 'STRONG_SIGNAL', 'ALERT', 'WARNING'
  priority             String   // 'LOW', 'MEDIUM', 'HIGH'

  // Signal data (stored directly for quick access)
  symbol               String?  // Coin symbol that triggered the notification
  signal               String?  // 'BUY', 'SELL', 'HOLD'
  confidence           Float?   // Confidence percentage (0-100)
  strength             Float?   // Signal strength (0-100)
  timeframe            String?  // Timeframe that triggered the notification

  // Technical analysis data (stored at notification creation)
  technicalIndicators  Json?    // Technical indicators data (RSI, MACD, EMA, Bollinger)
  chartPatterns        Json?    // Detected chart patterns
  candlestickPatterns  Json?    // Detected candlestick patterns
  triggeredTimeframes  Json?    // Array of timeframes that triggered this notification
  analysisReasoning    Json?    // Array of reasoning strings for the analysis
  currentPrice         Float?   // Price at the time of notification
  exchange             String?  // Exchange where the signal was detected

  // Notification settings
  hasVisual            Boolean  @default(true)
  isRead               Boolean  @default(false)

  // Relations
  ruleId               String?
  rule                 NotificationRule? @relation(fields: [ruleId], references: [id])
  signalId             String?
  signalHistory        SignalHistory? @relation(fields: [signalId], references: [id])

  createdAt            DateTime @default(now())
  readAt               DateTime?

  @@map("notifications")
  @@index([createdAt, isRead])
  @@index([type, priority, createdAt])
  @@index([symbol, signal, createdAt])
}

// Early Warning Alerts for pump/dump predictions
model EarlyWarningAlert {
  id                   String   @id @default(cuid())

  // Basic alert info
  symbol               String
  exchange             String   @default("binance")
  alertType            String   // 'PUMP_LIKELY', 'DUMP_LIKELY', 'NEUTRAL'

  // Confidence and timing
  confidence           Float    // 0-100 overall confidence score
  timeEstimateMin      Int      // Minimum time estimate in minutes
  timeEstimateMax      Int      // Maximum time estimate in minutes

  // Phase 1: Volume & Momentum Detection
  volumeSpike          Json?    // { detected: boolean, currentVolume: number, avgVolume: number, ratio: number, priceChange: number }
  multiTimeframeVolumeAnalysis Json? // { detected: boolean, symbol: string, timeframes: array, overallSignal: string, strongestTimeframe: string }
  rsiMomentum          Json?    // { detected: boolean, currentRSI: number, previousRSI: number, velocity: number, timeframe: string }
  emaConvergence       Json?    // { detected: boolean, ema20: number, ema50: number, gap: number, momentum: number }

  // Phase 2: Order Flow Analysis
  bidAskImbalance      Json?    // { detected: boolean, buyPressure: number, sellPressure: number, ratio: number, largeOrders: array }
  priceAction          Json?    // { detected: boolean, spreadChange: number, unusualOrders: array, icebergOrders: array }

  // Phase 3: Whale Activity
  whaleActivity        Json?    // { detected: boolean, transfers: array, totalAmount: number, direction: string, exchanges: array }

  // Scoring breakdown
  phase1Score          Float    @default(0) // Volume & Momentum score (0-100)
  phase2Score          Float    @default(0) // Order Flow score (0-100)
  phase3Score          Float    @default(0) // Whale Activity score (0-100)

  // Triggered conditions
  triggeredBy          Json     // Array of trigger names: ["Volume Spike", "RSI Momentum", "Whale Activity"]

  // Market data at alert time
  currentPrice         Float
  volume24h            Float?
  priceChange24h       Float?

  // Alert status
  isActive             Boolean  @default(true)
  isResolved           Boolean  @default(false)
  resolvedAt           DateTime?
  actualOutcome        String?  // 'PUMP_CONFIRMED', 'DUMP_CONFIRMED', 'FALSE_SIGNAL', 'PENDING'

  // Performance tracking
  accuracyScore        Float?   // 0-100 score based on actual outcome
  responseTime         Int?     // Time in minutes from alert to actual move

  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt

  @@map("early_warning_alerts")
  @@index([symbol, alertType, createdAt])
  @@index([confidence, createdAt])
  @@index([isActive, isResolved, createdAt])
  @@index([actualOutcome, accuracyScore])
}





// Early Warning Alert Rules for pump/dump prediction alerts
model EarlyWarningAlertRule {
  id                String   @id @default(cuid())
  name              String   // User-friendly name for the rule
  description       String?  // Optional description

  // Rule conditions
  minConfidence     Float    // Minimum confidence required (0-100)
  alertTypes        Json     // Array of alert types to monitor: ["PUMP_LIKELY", "DUMP_LIKELY"]
  requiredPhases    Json?    // Array of phases that must trigger: ["phase1", "phase2", "phase3"] or null for any
  minPhaseScore     Float?   // Minimum score required for individual phases (0-100)

  // Advanced conditions
  minTimeEstimate   Int?     // Minimum time estimate in minutes
  maxTimeEstimate   Int?     // Maximum time estimate in minutes
  requiredTriggers  Json?    // Array of required trigger types: ["Volume Spike", "RSI Momentum", "EMA Convergence", "Whale Activity"]

  // Alert settings
  priority          String   @default("MEDIUM") // 'LOW', 'MEDIUM', 'HIGH'
  enableToast       Boolean  @default(true)     // Show toast notification
  enableSound       Boolean  @default(true)     // Play sound notification

  // Status
  isActive          Boolean  @default(true)
  lastTriggered     DateTime?
  triggerCount      Int      @default(0)

  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  triggeredAlerts   EarlyWarningAlertHistory[]

  @@map("early_warning_alert_rules")
  @@index([isActive])
  @@index([minConfidence, isActive])
  @@index([priority, isActive])
}

// Early Warning Alert History for tracking triggered early warning alerts
model EarlyWarningAlertHistory {
  id                String   @id @default(cuid())

  // Alert rule reference
  ruleId            String
  ruleName          String
  rule              EarlyWarningAlertRule @relation(fields: [ruleId], references: [id], onDelete: Cascade)

  // Early warning alert reference
  earlyWarningId    String   // Reference to the EarlyWarningAlert that triggered this

  // Coin and alert info
  symbol            String
  alertType         String   // 'PUMP_LIKELY', 'DUMP_LIKELY'
  confidence        Float    // Confidence percentage (0-100)
  timeEstimateMin   Int      // Time estimate minimum in minutes
  timeEstimateMax   Int      // Time estimate maximum in minutes

  // Phase scores that triggered the alert
  phase1Score       Float
  phase2Score       Float
  phase3Score       Float
  triggeredBy       Json     // Array of trigger names that caused this alert

  // Market data at alert time
  currentPrice      Float
  volume24h         Float?
  priceChange24h    Float?

  // Phase analysis data (stored for reference)
  volumeSpike       Json?    // Volume spike analysis data
  rsiMomentum       Json?    // RSI momentum analysis data
  emaConvergence    Json?    // EMA convergence analysis data
  bidAskImbalance   Json?    // Bid/ask imbalance analysis data
  priceAction       Json?    // Price action analysis data
  whaleActivity     Json?    // Whale activity analysis data

  // Alert details
  message           String
  priority          String

  // Notification status
  toastShown        Boolean  @default(false)
  isRead            Boolean  @default(false)
  readAt            DateTime?

  // Timestamps
  triggeredAt       DateTime @default(now())

  @@map("early_warning_alert_history")
  @@index([symbol, triggeredAt])
  @@index([ruleId, triggeredAt])
  @@index([alertType, confidence, triggeredAt])
  @@index([isRead, triggeredAt])
}
