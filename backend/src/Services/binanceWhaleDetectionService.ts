import { logInfo, logError } from '../utils/logger';
import { BinanceService } from './binanceService';

interface LargeOrder {
  price: string;
  qty: string;
  count: number;
  value: number;
  direction: 'BID' | 'ASK';
  firstSeen: number;
  lastSeen: number;
  persistence: number; // How long the order has been in the book (seconds)
}

interface TradeData {
  symbol: string;
  price: string;
  quantity: string;
  time: number;
  isBuyerMaker: boolean;
  tradeValue: number;
  tradeId?: string;
  isWhaleSize: boolean;
  direction: 'BUY' | 'SELL';
  priceImpact?: number;
}

interface OrderBookLevel {
  price: string;
  quantity: string;
  value: number;
}

interface WhaleDetectionResult {
  detected: boolean;
  confidence: number;
  whaleActivity: {
    largeOrders: {
      bids: LargeOrder[];
      asks: LargeOrder[];
      totalBidVolume: number;
      totalAskVolume: number;
      persistentBidWalls: number;
      persistentAskWalls: number;
      directionalAnalysis: any;
    };
    largeTrades: {
      trades: TradeData[];
      recentWhaleActivity: TradeData[];
      analysis: any;
    };
    volumeSpike: {
      detected: boolean;
      currentVolume: number;
      averageVolume: number;
      spikeRatio: number;
    };
    volumeProfile: {
      detected: boolean;
      hotspots: any[];
      analysis: any;
    };
    orderBookImbalance: {
      ratio: number;
      direction: 'BULLISH' | 'BEARISH' | 'NEUTRAL';
      largeWalls: {
        bidWalls: number;
        askWalls: number;
      };
    };
    priceImpact: {
      detected: boolean;
      impact: number;
      direction: 'UP' | 'DOWN' | 'NEUTRAL';
      correlation: any;
      analysis: any;
    };
  };
  score: number;
  transferDirection: 'ACCUMULATION' | 'DISTRIBUTION' | 'LARGE_TRANSFER' | 'UNKNOWN';
  estimatedValue: number;
  hybridAnalysis: {
    orderBookScore: number;
    tradeAnalysisScore: number;
    volumeProfileScore: number;
    priceImpactScore: number;
    overallSignal: 'BULLISH' | 'BEARISH' | 'NEUTRAL';
    confidenceFactors: string[];
  };
}

export class BinanceWhaleDetectionService {
  private binanceService: BinanceService;
  private volumeHistory = new Map<string, number[]>();
  private priceHistory = new Map<string, number[]>();
  private tradeHistory = new Map<string, TradeData[]>();
  private orderPersistenceTracker = new Map<string, Map<string, LargeOrder>>(); // symbol -> orderKey -> order
  private volumeProfileData = new Map<string, Map<string, number>>(); // symbol -> priceLevel -> volume

  // Whale detection thresholds
  private readonly LARGE_ORDER_THRESHOLD_USD = 50000; // $50k+ orders
  private readonly LARGE_TRADE_THRESHOLD_USD = 25000; // $25k+ trades
  private readonly VOLUME_SPIKE_THRESHOLD = 2.5; // 2.5x average volume
  private readonly PRICE_IMPACT_THRESHOLD = 0.5; // 0.5% price impact
  private readonly MAX_HISTORY_LENGTH = 100; // Keep last 100 data points
  private readonly MIN_ORDER_PERSISTENCE_SECONDS = 30; // Minimum 30 seconds to be considered persistent

  constructor(binanceService?: BinanceService) {
    if (!binanceService) {
      throw new Error('BinanceWhaleDetectionService requires a BinanceService instance');
    }
    this.binanceService = binanceService;
    logInfo('BinanceWhaleDetectionService initialized with shared WebSocket data');
  }

  /**
   * Detect whale activity for a specific symbol using WebSocket data
   */
  async detectWhaleActivity(symbol: string): Promise<WhaleDetectionResult> {
    try {
      // Get cached data from BinanceService WebSocket streams
      const ticker = this.binanceService.getCachedPrice(symbol);
      const orderBook = this.binanceService.getCachedOrderBook(symbol);

      if (!ticker) {
        logError(`No ticker data available for ${symbol}`);
        return this.getEmptyResult();
      }

      const currentPrice = parseFloat(ticker.price);
      const currentVolume = parseFloat(ticker.volume);
      const priceChange24h = parseFloat(ticker.priceChangePercent);

      // Store trade data from ticker updates (simulated large trades detection)
      this.updateTradeHistory(symbol, currentPrice, currentVolume, 1);

      // Analyze large orders in order book (if available)
      const largeOrders = orderBook ?
        this.analyzeLargeOrdersFromCache(orderBook, currentPrice) :
        this.getEmptyLargeOrders();

      // Analyze large trades from our trade history
      const largeTrades = this.analyzeLargeTradesFromHistory(symbol, currentPrice);

      // Analyze volume spike using historical data
      const volumeSpike = this.analyzeVolumeSpike(symbol, currentVolume);

      // Analyze volume profile for concentration patterns
      const volumeProfile = this.analyzeVolumeProfile(symbol, currentPrice);

      // Analyze order book imbalance (if order book available)
      const orderBookImbalance = orderBook ?
        this.analyzeOrderBookImbalanceFromCache(orderBook, currentPrice) :
        this.getEmptyOrderBookImbalance();

      // Analyze price impact from recent price movements
      const priceImpact = this.analyzePriceImpactFromHistory(symbol, currentPrice);

      // Calculate hybrid whale detection score
      const hybridAnalysis = this.calculateHybridWhaleScore(largeOrders, largeTrades, volumeSpike, volumeProfile, orderBookImbalance, priceImpact);

      // Determine transfer direction
      const transferDirection = this.determineTransferDirection(orderBookImbalance, largeTrades, priceImpact, priceChange24h);

      // Estimate total value of whale activity
      const estimatedValue = this.estimateWhaleValue(largeOrders, largeTrades);

      const result: WhaleDetectionResult = {
        detected: hybridAnalysis.totalScore >= 30, // Minimum 30 points for whale detection
        confidence: Math.min(hybridAnalysis.totalScore, 100),
        whaleActivity: {
          largeOrders,
          largeTrades,
          volumeSpike,
          volumeProfile,
          orderBookImbalance,
          priceImpact
        },
        score: hybridAnalysis.totalScore,
        transferDirection,
        estimatedValue,
        hybridAnalysis
      };

      if (result.detected) {
        logInfo(`Whale activity detected for ${symbol}:`, {
          score: result.score,
          direction: result.transferDirection,
          value: result.estimatedValue,
          largeTradesCount: largeTrades.length,
          volumeSpike: volumeSpike.detected
        });
      }

      return result;

    } catch (error) {
      logError(`Error detecting whale activity for ${symbol}`, error as Error);
      return this.getEmptyResult();
    }
  }

  /**
   * Update trade history from ticker data
   */
  private updateTradeHistory(symbol: string, price: number, volume: number, tradeCount: number): void {
    const now = Date.now();
    const tradeValue = price * volume;

    // Determine trade direction from price movement
    const priceHistory = this.priceHistory.get(symbol) || [];
    let direction: 'BUY' | 'SELL' = 'BUY';
    let isBuyerMaker = false;

    if (priceHistory.length > 0) {
      const lastPrice = priceHistory[priceHistory.length - 1];
      if (price > lastPrice) {
        direction = 'BUY';
        isBuyerMaker = false; // Market buy order
      } else if (price < lastPrice) {
        direction = 'SELL';
        isBuyerMaker = true; // Market sell order
      }
    }

    const tradeData: TradeData = {
      symbol,
      price: price.toString(),
      quantity: volume.toString(),
      time: now,
      isBuyerMaker,
      tradeValue,
      tradeId: `${symbol}_${now}_${Math.random().toString(36).substring(2, 9)}`,
      isWhaleSize: tradeValue >= this.LARGE_TRADE_THRESHOLD_USD,
      direction
    };

    // Update price history for direction detection
    priceHistory.push(price);
    if (priceHistory.length > this.MAX_HISTORY_LENGTH) {
      priceHistory.splice(0, priceHistory.length - this.MAX_HISTORY_LENGTH);
    }
    this.priceHistory.set(symbol, priceHistory);

    // Update trade history
    let history = this.tradeHistory.get(symbol) || [];
    history.push(tradeData);

    // Keep only recent trades (last 100)
    if (history.length > this.MAX_HISTORY_LENGTH) {
      history = history.slice(-this.MAX_HISTORY_LENGTH);
    }

    this.tradeHistory.set(symbol, history);

    // Update volume profile data
    this.updateVolumeProfile(symbol, price, volume);
  }

  /**
   * Update volume profile data for price level analysis
   */
  private updateVolumeProfile(symbol: string, price: number, volume: number): void {
    // Round price to reasonable precision for grouping
    const priceLevel = (Math.round(price * 100) / 100).toString(); // 2 decimal places

    if (!this.volumeProfileData.has(symbol)) {
      this.volumeProfileData.set(symbol, new Map());
    }

    const symbolProfile = this.volumeProfileData.get(symbol)!;
    const currentVolume = symbolProfile.get(priceLevel) || 0;
    symbolProfile.set(priceLevel, currentVolume + volume);

    // Clean up old price levels (keep only recent ones)
    if (symbolProfile.size > 200) {
      const entries = Array.from(symbolProfile.entries());
      entries.sort((a, b) => parseFloat(b[0]) - parseFloat(a[0])); // Sort by price descending
      const topEntries = entries.slice(0, 150); // Keep top 150 price levels

      symbolProfile.clear();
      topEntries.forEach(([priceLevel, volume]) => {
        symbolProfile.set(priceLevel, volume);
      });
    }
  }

  /**
   * Get empty large orders structure
   */
  private getEmptyLargeOrders(): any {
    return {
      bids: [],
      asks: [],
      totalBidVolume: 0,
      totalAskVolume: 0
    };
  }

  /**
   * Get empty order book imbalance structure
   */
  private getEmptyOrderBookImbalance(): any {
    return {
      ratio: 1,
      direction: 'NEUTRAL' as const,
      largeWalls: {
        bidWalls: 0,
        askWalls: 0
      }
    };
  }

  /**
   * Analyze large orders from cached order book with persistence tracking
   */
  private analyzeLargeOrdersFromCache(orderBook: any, currentPrice: number): any {
    const symbol = orderBook.symbol || 'UNKNOWN';
    const now = Date.now();
    const largeBids: LargeOrder[] = [];
    const largeAsks: LargeOrder[] = [];
    let totalBidVolume = 0;
    let totalAskVolume = 0;
    let persistentBidWalls = 0;
    let persistentAskWalls = 0;

    // Get or create order tracker for this symbol
    if (!this.orderPersistenceTracker.has(symbol)) {
      this.orderPersistenceTracker.set(symbol, new Map());
    }
    const orderTracker = this.orderPersistenceTracker.get(symbol)!;

    // Analyze bids (buy orders) with persistence tracking
    orderBook.bids.forEach((level: any) => {
      const price = parseFloat(level.price);
      const qty = parseFloat(level.quantity);
      const orderValue = price * qty;

      if (orderValue >= this.LARGE_ORDER_THRESHOLD_USD) {
        const orderKey = `BID_${price.toFixed(8)}`;
        let order = orderTracker.get(orderKey);

        if (!order) {
          // New large order detected
          order = {
            price: price.toString(),
            qty: qty.toString(),
            count: 1,
            value: orderValue,
            direction: 'BID',
            firstSeen: now,
            lastSeen: now,
            persistence: 0
          };
          orderTracker.set(orderKey, order);
        } else {
          // Update existing order
          order.qty = qty.toString();
          order.value = orderValue;
          order.lastSeen = now;
          order.persistence = (now - order.firstSeen) / 1000; // seconds
        }

        largeBids.push(order);
        totalBidVolume += orderValue;

        // Count persistent walls (orders that have been in book for minimum time)
        if (order.persistence >= this.MIN_ORDER_PERSISTENCE_SECONDS) {
          persistentBidWalls++;
        }
      }
    });

    // Analyze asks (sell orders) with persistence tracking
    orderBook.asks.forEach((level: any) => {
      const price = parseFloat(level.price);
      const qty = parseFloat(level.quantity);
      const orderValue = price * qty;

      if (orderValue >= this.LARGE_ORDER_THRESHOLD_USD) {
        const orderKey = `ASK_${price.toFixed(8)}`;
        let order = orderTracker.get(orderKey);

        if (!order) {
          // New large order detected
          order = {
            price: price.toString(),
            qty: qty.toString(),
            count: 1,
            value: orderValue,
            direction: 'ASK',
            firstSeen: now,
            lastSeen: now,
            persistence: 0
          };
          orderTracker.set(orderKey, order);
        } else {
          // Update existing order
          order.qty = qty.toString();
          order.value = orderValue;
          order.lastSeen = now;
          order.persistence = (now - order.firstSeen) / 1000; // seconds
        }

        largeAsks.push(order);
        totalAskVolume += orderValue;

        // Count persistent walls (orders that have been in book for minimum time)
        if (order.persistence >= this.MIN_ORDER_PERSISTENCE_SECONDS) {
          persistentAskWalls++;
        }
      }
    });

    // Clean up old orders that are no longer in the order book
    this.cleanupStaleOrders(symbol, now);

    return {
      bids: largeBids,
      asks: largeAsks,
      totalBidVolume,
      totalAskVolume,
      persistentBidWalls,
      persistentAskWalls,
      directionalAnalysis: {
        bullishSignal: persistentBidWalls > persistentAskWalls && totalBidVolume > totalAskVolume,
        bearishSignal: persistentAskWalls > persistentBidWalls && totalAskVolume > totalBidVolume,
        bidAskRatio: totalAskVolume > 0 ? totalBidVolume / totalAskVolume : 1,
        wallImbalance: persistentBidWalls - persistentAskWalls
      }
    };
  }

  /**
   * Clean up stale orders that are no longer in the order book
   */
  private cleanupStaleOrders(symbol: string, currentTime: number): void {
    const orderTracker = this.orderPersistenceTracker.get(symbol);
    if (!orderTracker) return;

    const staleThreshold = 60000; // 1 minute
    const keysToDelete: string[] = [];

    orderTracker.forEach((order, key) => {
      if (currentTime - order.lastSeen > staleThreshold) {
        keysToDelete.push(key);
      }
    });

    keysToDelete.forEach(key => orderTracker.delete(key));
  }

  /**
   * Analyze large trades from history with enhanced whale detection
   */
  private analyzeLargeTradesFromHistory(symbol: string, currentPrice: number): any {
    const history = this.tradeHistory.get(symbol) || [];
    const now = Date.now();
    const oneHourAgo = now - (60 * 60 * 1000);
    const fifteenMinutesAgo = now - (15 * 60 * 1000);

    // Filter for recent large trades
    const recentLargeTrades = history.filter(trade => {
      const tradeValue = parseFloat(trade.price) * parseFloat(trade.quantity);
      const isRecent = trade.time >= oneHourAgo;
      return tradeValue >= this.LARGE_TRADE_THRESHOLD_USD && isRecent;
    });

    // Analyze trade patterns in last 15 minutes for immediate whale activity
    const recentWhaleTrades = history.filter(trade => {
      const tradeValue = parseFloat(trade.price) * parseFloat(trade.quantity);
      const isVeryRecent = trade.time >= fifteenMinutesAgo;
      return tradeValue >= this.LARGE_TRADE_THRESHOLD_USD && isVeryRecent;
    });

    // Calculate directional bias
    const buyTrades = recentLargeTrades.filter(trade => trade.direction === 'BUY');
    const sellTrades = recentLargeTrades.filter(trade => trade.direction === 'SELL');

    const totalBuyValue = buyTrades.reduce((sum, trade) => sum + trade.tradeValue, 0);
    const totalSellValue = sellTrades.reduce((sum, trade) => sum + trade.tradeValue, 0);

    const buyVsSellRatio = totalSellValue > 0 ? totalBuyValue / totalSellValue : 1;

    // Detect trade clustering (multiple large trades in short time)
    const tradeCluster = this.detectTradeCluster(recentWhaleTrades);

    // Calculate average trade size
    const avgTradeSize = recentLargeTrades.length > 0
      ? recentLargeTrades.reduce((sum, trade) => sum + trade.tradeValue, 0) / recentLargeTrades.length
      : 0;

    return {
      trades: recentLargeTrades,
      recentWhaleActivity: recentWhaleTrades,
      analysis: {
        totalTrades: recentLargeTrades.length,
        recentTradeCount: recentWhaleTrades.length,
        buyTradeCount: buyTrades.length,
        sellTradeCount: sellTrades.length,
        totalBuyValue,
        totalSellValue,
        buyVsSellRatio,
        avgTradeSize,
        dominantDirection: buyVsSellRatio > 1.5 ? 'BUY' : buyVsSellRatio < 0.67 ? 'SELL' : 'NEUTRAL',
        clusterDetected: tradeCluster.detected,
        clusterIntensity: tradeCluster.intensity,
        signal: this.determineTradeSignal(buyVsSellRatio, tradeCluster, recentWhaleTrades.length)
      }
    };
  }

  /**
   * Detect trade clustering (multiple large trades in short time)
   */
  private detectTradeCluster(trades: TradeData[]): { detected: boolean; intensity: number } {
    if (trades.length < 2) {
      return { detected: false, intensity: 0 };
    }

    const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);
    const recentTrades = trades.filter(trade => trade.time >= fiveMinutesAgo);

    // Calculate time gaps between trades
    const sortedTrades = recentTrades.sort((a, b) => a.time - b.time);
    const timeGaps: number[] = [];

    for (let i = 1; i < sortedTrades.length; i++) {
      const gap = (sortedTrades[i].time - sortedTrades[i-1].time) / 1000; // seconds
      timeGaps.push(gap);
    }

    // Cluster detected if multiple trades within short intervals
    const shortGaps = timeGaps.filter(gap => gap < 60); // Less than 1 minute apart
    const intensity = recentTrades.length > 0 ? (shortGaps.length / timeGaps.length) : 0;

    return {
      detected: recentTrades.length >= 3 && intensity > 0.5,
      intensity: Math.round(intensity * 100) / 100
    };
  }

  /**
   * Determine trade signal based on analysis
   */
  private determineTradeSignal(buyVsSellRatio: number, cluster: any, recentTradeCount: number): 'BULLISH' | 'BEARISH' | 'NEUTRAL' {
    let bullishSignals = 0;
    let bearishSignals = 0;

    // Buy/sell ratio signals
    if (buyVsSellRatio > 2) bullishSignals += 2;
    else if (buyVsSellRatio > 1.5) bullishSignals += 1;
    else if (buyVsSellRatio < 0.5) bearishSignals += 2;
    else if (buyVsSellRatio < 0.67) bearishSignals += 1;

    // Cluster signals
    if (cluster.detected && cluster.intensity > 0.7) {
      if (buyVsSellRatio > 1) bullishSignals += 1;
      else bearishSignals += 1;
    }

    // Recent activity signals
    if (recentTradeCount >= 5) {
      if (buyVsSellRatio > 1) bullishSignals += 1;
      else bearishSignals += 1;
    }

    if (bullishSignals > bearishSignals) return 'BULLISH';
    if (bearishSignals > bullishSignals) return 'BEARISH';
    return 'NEUTRAL';
  }

  /**
   * Analyze volume profile for unusual concentrations
   */
  private analyzeVolumeProfile(symbol: string, currentPrice: number): any {
    const profileData = this.volumeProfileData.get(symbol);
    if (!profileData || profileData.size < 10) {
      return {
        detected: false,
        hotspots: [],
        analysis: {
          totalLevels: 0,
          avgVolumePerLevel: 0,
          maxVolumeLevel: null,
          concentrationRatio: 0,
          signal: 'NEUTRAL'
        }
      };
    }

    const entries = Array.from(profileData.entries());
    const volumes = entries.map(([_, volume]) => volume);
    const totalVolume = volumes.reduce((sum, vol) => sum + vol, 0);
    const avgVolume = totalVolume / volumes.length;

    // Find volume hotspots (levels with significantly higher volume)
    const hotspotThreshold = avgVolume * 3; // 3x average volume
    const hotspots = entries
      .filter(([_, volume]) => volume >= hotspotThreshold)
      .map(([priceLevel, volume]) => ({
        price: parseFloat(priceLevel),
        volume,
        ratio: volume / avgVolume,
        distanceFromCurrent: Math.abs(parseFloat(priceLevel) - currentPrice),
        distancePercent: Math.abs((parseFloat(priceLevel) - currentPrice) / currentPrice) * 100
      }))
      .sort((a, b) => b.volume - a.volume); // Sort by volume descending

    // Find the maximum volume level
    const maxVolumeEntry = entries.reduce((max, current) =>
      current[1] > max[1] ? current : max
    );

    const maxVolumeLevel = {
      price: parseFloat(maxVolumeEntry[0]),
      volume: maxVolumeEntry[1],
      ratio: maxVolumeEntry[1] / avgVolume
    };

    // Calculate concentration ratio (how much volume is concentrated in top levels)
    const topLevels = entries.sort((a, b) => b[1] - a[1]).slice(0, 5);
    const topVolume = topLevels.reduce((sum, [_, vol]) => sum + vol, 0);
    const concentrationRatio = topVolume / totalVolume;

    // Determine signal based on hotspot proximity to current price
    let signal: 'BULLISH' | 'BEARISH' | 'NEUTRAL' = 'NEUTRAL';
    const nearbyHotspots = hotspots.filter(h => h.distancePercent < 2); // Within 2% of current price

    if (nearbyHotspots.length > 0) {
      const nearestHotspot = nearbyHotspots[0];
      if (nearestHotspot.price > currentPrice) {
        signal = 'BULLISH'; // Volume concentration above current price (resistance turned support)
      } else {
        signal = 'BEARISH'; // Volume concentration below current price (support turned resistance)
      }
    }

    return {
      detected: hotspots.length > 0 || concentrationRatio > 0.4,
      hotspots: hotspots.slice(0, 10), // Top 10 hotspots
      analysis: {
        totalLevels: profileData.size,
        avgVolumePerLevel: Math.round(avgVolume),
        maxVolumeLevel,
        concentrationRatio: Math.round(concentrationRatio * 100) / 100,
        signal,
        nearbyHotspotsCount: nearbyHotspots.length
      }
    };
  }

  /**
   * Analyze order book imbalance from cached data
   */
  private analyzeOrderBookImbalanceFromCache(orderBook: any, currentPrice: number): any {
    if (!orderBook.bids || !orderBook.asks) {
      return this.getEmptyOrderBookImbalance();
    }

    const topBids = orderBook.bids.slice(0, 10);
    const topAsks = orderBook.asks.slice(0, 10);

    const bidVolume = topBids.reduce((sum: number, level: any) =>
      sum + (level.price * level.quantity), 0);
    const askVolume = topAsks.reduce((sum: number, level: any) =>
      sum + (level.price * level.quantity), 0);

    const ratio = askVolume > 0 ? bidVolume / askVolume : 1;
    let direction: 'BULLISH' | 'BEARISH' | 'NEUTRAL' = 'NEUTRAL';

    if (ratio > 1.5) direction = 'BULLISH';
    else if (ratio < 0.67) direction = 'BEARISH';

    // Count large walls (orders > $100k)
    const bidWalls = topBids.filter((level: any) =>
      level.price * level.quantity > 100000
    ).length;

    const askWalls = topAsks.filter((level: any) =>
      level.price * level.quantity > 100000
    ).length;

    return {
      ratio,
      direction,
      largeWalls: {
        bidWalls,
        askWalls
      }
    };
  }

  /**
   * Analyze price impact correlation with whale activity
   */
  private analyzePriceImpactFromHistory(symbol: string, currentPrice: number): any {
    const priceHistory = this.priceHistory.get(symbol) || [];
    const tradeHistory = this.tradeHistory.get(symbol) || [];

    if (priceHistory.length < 10) {
      return {
        detected: false,
        impact: 0,
        direction: 'NEUTRAL',
        correlation: {
          whaleTradeCorrelation: 0,
          immediateImpact: 0,
          sustainedImpact: 0,
          efficiency: 0
        }
      };
    }

    // Analyze different time windows for price impact
    const shortTermPrices = priceHistory.slice(-5);  // Last 5 data points
    const mediumTermPrices = priceHistory.slice(-10); // Last 10 data points
    const longTermPrices = priceHistory.slice(-20);   // Last 20 data points

    // Calculate price changes for different periods
    const shortTermChange = this.calculatePriceChange(shortTermPrices);
    const mediumTermChange = this.calculatePriceChange(mediumTermPrices);
    const longTermChange = this.calculatePriceChange(longTermPrices);

    // Analyze correlation with whale trades
    const recentWhaleTradeCorrelation = this.analyzeWhaleTradeCorrelation(symbol, priceHistory, tradeHistory);

    // Calculate price impact efficiency (how much price moved per unit of whale activity)
    const efficiency = this.calculatePriceImpactEfficiency(recentWhaleTradeCorrelation, mediumTermChange);

    // Determine overall impact
    const impact = Math.abs(mediumTermChange.percentChange);
    const detected = impact >= this.PRICE_IMPACT_THRESHOLD || recentWhaleTradeCorrelation.strongCorrelation;

    let direction: 'UP' | 'DOWN' | 'NEUTRAL' = 'NEUTRAL';
    if (mediumTermChange.percentChange > 0.3) direction = 'UP';
    else if (mediumTermChange.percentChange < -0.3) direction = 'DOWN';

    return {
      detected,
      impact,
      direction,
      correlation: {
        whaleTradeCorrelation: recentWhaleTradeCorrelation.correlationScore,
        immediateImpact: Math.abs(shortTermChange.percentChange),
        sustainedImpact: Math.abs(longTermChange.percentChange),
        efficiency: efficiency,
        strongCorrelation: recentWhaleTradeCorrelation.strongCorrelation
      },
      analysis: {
        shortTerm: shortTermChange,
        mediumTerm: mediumTermChange,
        longTerm: longTermChange,
        whaleActivity: recentWhaleTradeCorrelation.whaleActivity,
        signal: this.determinePriceImpactSignal(direction, recentWhaleTradeCorrelation, efficiency)
      }
    };
  }

  /**
   * Calculate price change for a given price array
   */
  private calculatePriceChange(prices: number[]): any {
    if (prices.length < 2) {
      return { absoluteChange: 0, percentChange: 0, volatility: 0 };
    }

    const startPrice = prices[0];
    const endPrice = prices[prices.length - 1];
    const absoluteChange = endPrice - startPrice;
    const percentChange = (absoluteChange / startPrice) * 100;

    // Calculate volatility (standard deviation of price changes)
    const priceChanges = [];
    for (let i = 1; i < prices.length; i++) {
      priceChanges.push(((prices[i] - prices[i-1]) / prices[i-1]) * 100);
    }

    const avgChange = priceChanges.reduce((sum, change) => sum + change, 0) / priceChanges.length;
    const variance = priceChanges.reduce((sum, change) => sum + Math.pow(change - avgChange, 2), 0) / priceChanges.length;
    const volatility = Math.sqrt(variance);

    return {
      absoluteChange,
      percentChange,
      volatility,
      startPrice,
      endPrice
    };
  }

  /**
   * Analyze correlation between whale trades and price movements
   */
  private analyzeWhaleTradeCorrelation(symbol: string, priceHistory: number[], tradeHistory: TradeData[]): any {
    const now = Date.now();
    const thirtyMinutesAgo = now - (30 * 60 * 1000);

    // Get recent whale trades
    const recentWhaleTrades = tradeHistory.filter(trade =>
      trade.time >= thirtyMinutesAgo && trade.isWhaleSize
    );

    if (recentWhaleTrades.length === 0 || priceHistory.length < 5) {
      return {
        correlationScore: 0,
        strongCorrelation: false,
        whaleActivity: {
          tradeCount: 0,
          totalValue: 0,
          buyValue: 0,
          sellValue: 0
        }
      };
    }

    // Calculate whale activity metrics
    const totalValue = recentWhaleTrades.reduce((sum, trade) => sum + trade.tradeValue, 0);
    const buyTrades = recentWhaleTrades.filter(trade => trade.direction === 'BUY');
    const sellTrades = recentWhaleTrades.filter(trade => trade.direction === 'SELL');
    const buyValue = buyTrades.reduce((sum, trade) => sum + trade.tradeValue, 0);
    const sellValue = sellTrades.reduce((sum, trade) => sum + trade.tradeValue, 0);

    // Calculate price movement during whale activity period
    const recentPrices = priceHistory.slice(-10);
    const priceChange = this.calculatePriceChange(recentPrices);

    // Calculate correlation score
    let correlationScore = 0;

    // Directional correlation
    if (buyValue > sellValue && priceChange.percentChange > 0) {
      correlationScore += 30; // Whale buying + price up
    } else if (sellValue > buyValue && priceChange.percentChange < 0) {
      correlationScore += 30; // Whale selling + price down
    }

    // Volume correlation
    const volumeIntensity = Math.min((totalValue / 1000000) * 10, 40); // Scale by millions
    correlationScore += volumeIntensity;

    // Timing correlation (more recent trades = higher correlation)
    const avgTradeAge = recentWhaleTrades.reduce((sum, trade) => sum + (now - trade.time), 0) / recentWhaleTrades.length;
    const timingScore = Math.max(0, 30 - (avgTradeAge / (60 * 1000))); // Decrease score with age
    correlationScore += timingScore;

    const strongCorrelation = correlationScore >= 60;

    return {
      correlationScore: Math.round(correlationScore),
      strongCorrelation,
      whaleActivity: {
        tradeCount: recentWhaleTrades.length,
        totalValue,
        buyValue,
        sellValue,
        netFlow: buyValue - sellValue
      }
    };
  }

  /**
   * Calculate price impact efficiency
   */
  private calculatePriceImpactEfficiency(correlation: any, priceChange: any): number {
    if (correlation.whaleActivity.totalValue === 0) return 0;

    // Efficiency = price movement per million dollars of whale activity
    const efficiency = Math.abs(priceChange.percentChange) / (correlation.whaleActivity.totalValue / 1000000);
    return Math.round(efficiency * 100) / 100;
  }

  /**
   * Determine price impact signal
   */
  private determinePriceImpactSignal(direction: string, correlation: any, efficiency: number): 'BULLISH' | 'BEARISH' | 'NEUTRAL' {
    if (!correlation.strongCorrelation) return 'NEUTRAL';

    const netFlow = correlation.whaleActivity.netFlow;

    if (direction === 'UP' && netFlow > 0 && efficiency > 0.5) return 'BULLISH';
    if (direction === 'DOWN' && netFlow < 0 && efficiency > 0.5) return 'BEARISH';

    return 'NEUTRAL';
  }

  /**
   * Analyze volume spike using historical data
   */
  private analyzeVolumeSpike(symbol: string, currentVolume: number): any {
    // Get volume history for comparison
    let volumeHistory = this.volumeHistory.get(symbol) || [];
    volumeHistory.push(currentVolume);

    if (volumeHistory.length > this.MAX_HISTORY_LENGTH) {
      volumeHistory = volumeHistory.slice(-this.MAX_HISTORY_LENGTH);
    }

    this.volumeHistory.set(symbol, volumeHistory);

    if (volumeHistory.length < 10) {
      return {
        detected: false,
        currentVolume,
        averageVolume: currentVolume,
        spikeRatio: 1
      };
    }

    // Calculate average volume from historical data
    const averageVolume = volumeHistory.reduce((sum, vol) => sum + vol, 0) / volumeHistory.length;
    const spikeRatio = currentVolume / averageVolume;
    const detected = spikeRatio >= this.VOLUME_SPIKE_THRESHOLD;

    return {
      detected,
      currentVolume,
      averageVolume,
      spikeRatio
    };
  }



  /**
   * Calculate hybrid whale detection score with comprehensive analysis
   */
  private calculateHybridWhaleScore(largeOrders: any, largeTrades: any, volumeSpike: any, volumeProfile: any, orderBookImbalance: any, priceImpact: any): any {
    let orderBookScore = 0;
    let tradeAnalysisScore = 0;
    let volumeProfileScore = 0;
    let priceImpactScore = 0;
    const confidenceFactors: string[] = [];

    // 1. Order Book Analysis Score (max 35 points)
    if (largeOrders.bids.length > 0 || largeOrders.asks.length > 0) {
      orderBookScore += Math.min((largeOrders.bids.length + largeOrders.asks.length) * 3, 15);
      confidenceFactors.push(`${largeOrders.bids.length + largeOrders.asks.length} large orders detected`);
    }

    // Persistent walls bonus
    if (largeOrders.persistentBidWalls > 0 || largeOrders.persistentAskWalls > 0) {
      orderBookScore += Math.min((largeOrders.persistentBidWalls + largeOrders.persistentAskWalls) * 5, 15);
      confidenceFactors.push(`${largeOrders.persistentBidWalls + largeOrders.persistentAskWalls} persistent walls`);
    }

    // Directional analysis bonus
    if (largeOrders.directionalAnalysis?.bullishSignal || largeOrders.directionalAnalysis?.bearishSignal) {
      orderBookScore += 5;
      confidenceFactors.push('Strong directional order book signal');
    }

    // 2. Trade Analysis Score (max 30 points)
    if (largeTrades.analysis?.totalTrades > 0) {
      tradeAnalysisScore += Math.min(largeTrades.analysis.totalTrades * 3, 15);
      confidenceFactors.push(`${largeTrades.analysis.totalTrades} large trades`);
    }

    // Trade clustering bonus
    if (largeTrades.analysis?.clusterDetected) {
      tradeAnalysisScore += Math.min(largeTrades.analysis.clusterIntensity * 10, 10);
      confidenceFactors.push('Trade clustering detected');
    }

    // Directional trade bias
    if (largeTrades.analysis?.dominantDirection !== 'NEUTRAL') {
      tradeAnalysisScore += 5;
      confidenceFactors.push(`${largeTrades.analysis.dominantDirection} trade bias`);
    }

    // 3. Volume Profile Score (max 20 points)
    if (volumeProfile.detected) {
      volumeProfileScore += 10;
      confidenceFactors.push('Volume concentration detected');

      if (volumeProfile.analysis?.concentrationRatio > 0.5) {
        volumeProfileScore += 5;
        confidenceFactors.push('High volume concentration');
      }

      if (volumeProfile.analysis?.nearbyHotspotsCount > 0) {
        volumeProfileScore += 5;
        confidenceFactors.push('Volume hotspots near current price');
      }
    }

    // 4. Price Impact Score (max 15 points)
    if (priceImpact.detected) {
      priceImpactScore += Math.min(priceImpact.impact * 2, 10);
      confidenceFactors.push(`${priceImpact.impact.toFixed(2)}% price impact`);
    }

    // Strong correlation bonus
    if (priceImpact.correlation?.strongCorrelation) {
      priceImpactScore += 5;
      confidenceFactors.push('Strong whale-price correlation');
    }

    // Calculate total score
    const totalScore = orderBookScore + tradeAnalysisScore + volumeProfileScore + priceImpactScore;

    // Determine overall signal
    let overallSignal: 'BULLISH' | 'BEARISH' | 'NEUTRAL' = 'NEUTRAL';
    let bullishSignals = 0;
    let bearishSignals = 0;

    if (largeOrders.directionalAnalysis?.bullishSignal) bullishSignals++;
    if (largeOrders.directionalAnalysis?.bearishSignal) bearishSignals++;
    if (largeTrades.analysis?.signal === 'BULLISH') bullishSignals++;
    if (largeTrades.analysis?.signal === 'BEARISH') bearishSignals++;
    if (volumeProfile.analysis?.signal === 'BULLISH') bullishSignals++;
    if (volumeProfile.analysis?.signal === 'BEARISH') bearishSignals++;
    if (priceImpact.analysis?.signal === 'BULLISH') bullishSignals++;
    if (priceImpact.analysis?.signal === 'BEARISH') bearishSignals++;

    if (bullishSignals > bearishSignals && bullishSignals >= 2) overallSignal = 'BULLISH';
    else if (bearishSignals > bullishSignals && bearishSignals >= 2) overallSignal = 'BEARISH';

    return {
      totalScore: Math.round(totalScore),
      orderBookScore,
      tradeAnalysisScore,
      volumeProfileScore,
      priceImpactScore,
      overallSignal,
      confidenceFactors
    };
  }

  /**
   * Calculate overall whale detection score (legacy method for compatibility)
   */
  private calculateWhaleScore(largeOrders: any, largeTrades: any, volumeSpike: any, orderBookImbalance: any, priceImpact: any): number {
    let score = 0;

    // Large orders score (max 30 points)
    score += Math.min((largeOrders.bids.length + largeOrders.asks.length) * 5, 30);

    // Large trades score (max 25 points)
    const tradeCount = largeTrades.trades ? largeTrades.trades.length : largeTrades.length;
    score += Math.min(tradeCount * 5, 25);

    // Volume spike score (max 20 points)
    if (volumeSpike.detected) {
      score += Math.min(volumeSpike.spikeRatio * 5, 20);
    }

    // Order book imbalance score (max 15 points)
    if (orderBookImbalance.direction !== 'NEUTRAL') {
      score += 10;
      score += (orderBookImbalance.largeWalls.bidWalls + orderBookImbalance.largeWalls.askWalls) * 2.5;
    }

    // Price impact score (max 10 points)
    if (priceImpact.detected) {
      score += Math.min(priceImpact.impact * 2, 10);
    }

    return Math.round(score);
  }

  /**
   * Determine transfer direction based on analysis
   */
  private determineTransferDirection(orderBookImbalance: any, largeTrades: any, priceImpact: any, priceChange24h: number): 'ACCUMULATION' | 'DISTRIBUTION' | 'LARGE_TRANSFER' | 'UNKNOWN' {
    const bullishSignals = [
      orderBookImbalance.direction === 'BULLISH',
      priceImpact.direction === 'UP',
      priceChange24h > 2, // Price up more than 2%
      largeTrades.filter((t: any) => !t.isBuyerMaker).length > largeTrades.filter((t: any) => t.isBuyerMaker).length
    ].filter(Boolean).length;

    const bearishSignals = [
      orderBookImbalance.direction === 'BEARISH',
      priceImpact.direction === 'DOWN',
      priceChange24h < -2, // Price down more than 2%
      largeTrades.filter((t: any) => t.isBuyerMaker).length > largeTrades.filter((t: any) => !t.isBuyerMaker).length
    ].filter(Boolean).length;

    if (bullishSignals >= 2) return 'ACCUMULATION';
    if (bearishSignals >= 2) return 'DISTRIBUTION';
    if (largeTrades.length > 0) return 'LARGE_TRANSFER';
    return 'UNKNOWN';
  }

  /**
   * Estimate total value of whale activity
   */
  private estimateWhaleValue(largeOrders: any, largeTrades: any): number {
    const orderValue = largeOrders.totalBidVolume + largeOrders.totalAskVolume;
    const tradeValue = largeTrades.reduce((sum: number, trade: any) => 
      sum + (parseFloat(trade.price) * parseFloat(trade.qty)), 0
    );
    
    return orderValue + tradeValue;
  }

  /**
   * Get empty result for error cases
   */
  private getEmptyResult(): WhaleDetectionResult {
    return {
      detected: false,
      confidence: 0,
      whaleActivity: {
        largeOrders: {
          bids: [],
          asks: [],
          totalBidVolume: 0,
          totalAskVolume: 0,
          persistentBidWalls: 0,
          persistentAskWalls: 0,
          directionalAnalysis: {
            bullishSignal: false,
            bearishSignal: false,
            bidAskRatio: 1,
            wallImbalance: 0
          }
        },
        largeTrades: {
          trades: [],
          recentWhaleActivity: [],
          analysis: {
            totalTrades: 0,
            recentTradeCount: 0,
            buyTradeCount: 0,
            sellTradeCount: 0,
            totalBuyValue: 0,
            totalSellValue: 0,
            buyVsSellRatio: 1,
            avgTradeSize: 0,
            dominantDirection: 'NEUTRAL',
            clusterDetected: false,
            clusterIntensity: 0,
            signal: 'NEUTRAL'
          }
        },
        volumeSpike: { detected: false, currentVolume: 0, averageVolume: 0, spikeRatio: 0 },
        volumeProfile: {
          detected: false,
          hotspots: [],
          analysis: {
            totalLevels: 0,
            avgVolumePerLevel: 0,
            maxVolumeLevel: null,
            concentrationRatio: 0,
            signal: 'NEUTRAL'
          }
        },
        orderBookImbalance: { ratio: 1, direction: 'NEUTRAL', largeWalls: { bidWalls: 0, askWalls: 0 } },
        priceImpact: {
          detected: false,
          impact: 0,
          direction: 'NEUTRAL',
          correlation: {
            whaleTradeCorrelation: 0,
            immediateImpact: 0,
            sustainedImpact: 0,
            efficiency: 0
          },
          analysis: {
            shortTerm: { absoluteChange: 0, percentChange: 0, volatility: 0 },
            mediumTerm: { absoluteChange: 0, percentChange: 0, volatility: 0 },
            longTerm: { absoluteChange: 0, percentChange: 0, volatility: 0 },
            whaleActivity: { tradeCount: 0, totalValue: 0, buyValue: 0, sellValue: 0 },
            signal: 'NEUTRAL'
          }
        }
      },
      score: 0,
      transferDirection: 'UNKNOWN',
      estimatedValue: 0,
      hybridAnalysis: {
        orderBookScore: 0,
        tradeAnalysisScore: 0,
        volumeProfileScore: 0,
        priceImpactScore: 0,
        overallSignal: 'NEUTRAL',
        confidenceFactors: []
      }
    };
  }
}
